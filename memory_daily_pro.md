# 📝 ذاكرة التدخلات اليومية المتقدمة - Memory Daily Pro

## 🎯 الهدف العام
تطوير نظام التدخلات اليومية المتقدمة الكامل مع جميع النماذج والوظائف المطلوبة حسب المواصفات في الملفات:
- `التدخلات اليومية المتقدم.md`
- `جداول التدخلات المتقدم.md`

## ⚠️ تعليمات مهمة للوكيل الجديد

### 📖 قراءة إجبارية قبل البدء:
**يجب قراءة هذين الملفين بعناية فائقة لفهم كيفية عمل النظام:**

1. **`/Users/<USER>/Documents/Copy_Secure_DPC_DZ/DPC_DZ/جداول التدخلات المتقدم.md`**
   - يحتوي على تفاصيل جميع الجداول والحقول المطلوبة
   - يوضح البنية الكاملة لكل نوع تدخل
   - يحدد عدد الحقول لكل جدول بدقة

2. **`/Users/<USER>/Documents/Copy_Secure_DPC_DZ/DPC_DZ/التدخلات اليومية المتقدم.md`**
   - يشرح سير العمل الكامل للنظام
   - يوضح كيفية انتقال التدخل من مرحلة لأخرى
   - يحدد النماذج المطلوبة لكل مرحلة

### 🌐 الصفحات المطلوب فهمها:
1. **صفحة التدخلات اليومية:** http://127.0.0.1:8000/coordination-center/daily-interventions/
   - الصفحة الرئيسية لإدارة التدخلات اليومية
   - تحتوي على نماذج: البلاغ الأولي، عملية التعرف، إنهاء المهمة
   - يجب أن تعمل بتناسق مع الصفحة الثانية

2. **صفحة جميع التدخلات:** http://127.0.0.1:8000/coordination-center/all-interventions/
   - صفحة عرض الجداول المتقدمة
   - تعرض البيانات المحفوظة من الصفحة الأولى
   - يجب أن تعكس جميع البيانات المدخلة

### 🔗 التناسق المطلوب:
- ما يتم إدخاله في نماذج الصفحة الأولى يجب أن يظهر في جداول الصفحة الثانية
- الحقول في النماذج يجب أن تطابق أعمدة الجداول
- سير العمل يجب أن يكون متسلسل ومنطقي
- التصميم والواجهة يجب أن تكون متناسقة

---

## ✅ المهام المكتملة

### 1. إصلاح مشكلة عدم حفظ البلاغ الأولي ✅
**التاريخ:** 1 أغسطس 2025
**المشكلة:** خطأ "no such table: home_dailyintervention"
**الحل المطبق:**
- إصلاح دالة التحقق من الحقول في JavaScript (تحويل أسماء الحقول من snake_case إلى camelCase)
- إنشاء migration جديد `0050_restore_daily_intervention.py` لإعادة إنشاء جداول:
  - `DailyIntervention`
  - `InterventionVehicle`
- تطبيق migration بنجاح
- إصلاح دالة `get_interventions` لاستخدام النموذج الصحيح `DailyIntervention`

**الملفات المحدثة:**
- `dpcdz/templates/coordination_center/daily_interventions_empty.html` (السطور 1970-1992)
- `dpcdz/home/<USER>/0050_restore_daily_intervention.py` (ملف جديد)
- `dpcdz/home/<USER>

**النتيجة:** البلاغ الأولي يعمل الآن بشكل صحيح ويحفظ البيانات في قاعدة البيانات

---

## 🔄 المهام قيد التنفيذ

### 1. تطوير نماذج عملية التعرف 🔄
**الحالة:** بدأت - تحتاج إكمال
**المطلوب:** إنشاء 5 نماذج لعملية التعرف:

#### أ) نموذج عملية التعرف - الإجلاء الصحي
**الحقول المطلوبة:**
- ساعة الوصول
- موقع الإجلاء المحدد (داخل منزل، خارج منزل، مكان عمل، إلخ)
- نوع الإجلاء (الاختناق، التسممات، الحروق، الانفجارات، إجلاء المرضى، الغرقى)
- طبيعة الإجلاء (حسب النوع المختار)
- طلب الدعم (خيارات متعددة)
- ملاحظة بعد عملية التعرف (اختياري)

#### ب) نموذج عملية التعرف - حوادث المرور
**الحقول المطلوبة:**
- ساعة الوصول
- موقع الحادث المحدد (وسط المدينة، سوق أسبوعي، إلخ)
- نوع الحادث (ضحايا مصدومة بالمركبات، تصادم، انقلاب، مصدومة بالقطار، أخرى)
- طبيعة الحادث (حسب النوع المختار)
- طلب الدعم
- ملاحظة بعد عملية التعرف (اختياري)

#### ج) نموذج عملية التعرف - حريق المحاصيل الزراعية
**الحقول المطلوبة:**
- ساعة الوصول
- موقع الحريق المحدد (حقل، بستان، سهل، هضبة، إلخ)
- نوع المحصول المحترق (قمح واقف، حصيدة، شعير، حزم تبن، غابة/أحراش، إلخ) - متعدد الاختيار
- عدد البؤر (الموقد)
- اتجاه الرياح (شمالي، جنوبي، شرقي، إلخ)
- سرعة الرياح (كم/سا)
- تهديد للسكان (نعم/لا)
- عدد العائلات المتأثرة
- مكان إجلاء السكان (إن وُجد)
- ماهية المساعدات المقدمة للسكان (إن وُجد)
- الجهات الحاضرة (حراس الغابات، الأمن الوطني، الدرك الوطني، إلخ) - متعدد الاختيار
- طلب الدعم
- ملاحظة بعد عملية التعرف (اختياري)

#### د) نموذج عملية التعرف - حرائق البنايات والمؤسسات
**الحقول المطلوبة:**
- ساعة الوصول
- موقع الحريق (داخل بناية، خارج بناية، داخل منزل، خارج منزل، إلخ)
- نوع الحريق (حرائق البنايات السكنية، المؤسسات المصنفة، الأماكن المستقبلة للجمهور، المركبات)
- طبيعة الحريق (حسب النوع المختار)
- طابق معين (اختياري)
- غرفة محددة (اختياري)
- عدد نقاط الاشتعال
- تهديد السكان
- هل تم إجلاء السكان؟
- ماهية المساعدات المقدمة للسكان
- طلب الدعم
- ملاحظة بعد عملية التعرف (اختياري)

#### هـ) نموذج عملية التعرف - العمليات المختلفة
**الحقول المطلوبة:**
- ساعة الوصول
- موقع العملية المحدد (داخل منزل، خارج منزل، مكان عمل، إلخ)
- نوع العملية (تدخلات مختلفة، العمليات الاستثنائية، الجهاز الأمني)
- طبيعة العملية (حسب النوع المختار)
- طلب الدعم
- ملاحظة بعد عملية التعرف (اختياري)

---

## 📋 المهام المعلقة (لم تبدأ بعد)

### 1. إنشاء جداول التدخلات المتقدمة
**المطلوب:** إنشاء جداول التدخلات المتقدمة الخمسة:
- جدول الإجلاء الصحي
- جدول حوادث المرور
- جدول حريق المحاصيل الزراعية
- جدول حرائق البنايات والمؤسسات
- جدول العمليات المختلفة
**الموقع:** http://127.0.0.1:8000/coordination-center/all-interventions/

### 2. مقارنة الصفحات وتحديد الاختلافات
**المطلوب:** مقارنة صفحة التدخلات اليومية مع صفحة جميع التدخلات لتحديد ما هو مفقود

### 3. تطوير نماذج إنهاء المهمة
**المطلوب:** إنشاء 5 نماذج لإنهاء المهمة لكل نوع تدخل

### 4. تطوير جدول التدخلات التفاعلي
**المطلوب:** عرض البيانات الحقيقية مع فلاتر وأزرار إجراءات

### 5. تطوير نظام طلب الدعم
**المطلوب:** آلية طلب الدعم من الوحدات المجاورة مع الإنذارات الصوتية

### 6. تطوير نظام التصعيد إلى كارثة كبرى
**المطلوب:** آلية تصعيد التدخلات مع التحويل التلقائي

---

## 🔧 ملاحظات تقنية للوكيل الجديد

### البنية الحالية:
- **النموذج الرئيسي:** `DailyIntervention` في `dpcdz/home/<USER>
- **الصفحة الرئيسية:** `dpcdz/templates/coordination_center/daily_interventions_empty.html`
- **Views:** `dpcdz/home/<USER>
- **URLs:** `dpcdz/home/<USER>

### APIs الموجودة:
- `/api/create-initial-report/` - حفظ البلاغ الأولي ✅
- `/api/get-available-vehicles/` - جلب الوسائل المتاحة ✅
- `/api/get-interventions/` - جلب قائمة التدخلات ✅

### APIs المطلوبة:
- `/api/update-reconnaissance/` - تحديث بيانات عملية التعرف
- `/api/complete-intervention/` - إنهاء المهمة
- `/api/request-support/` - طلب الدعم
- `/api/escalate-intervention/` - تصعيد إلى كارثة كبرى

### الأولوية التالية:
1. **إكمال نماذج عملية التعرف** (الأهم)
2. إنشاء الجداول المتقدمة الخمسة
3. تطوير نماذج إنهاء المهمة
4. تطوير الجدول التفاعلي

### الوضع الحالي المؤكد:
- ✅ صفحة all-interventions موجودة وتعرض 5 بطاقات للجداول
- ❌ الجداول الفعلية لم يتم إنشاؤها بعد (تظهر رسالة "قيد التطوير")
- ✅ البلاغ الأولي يعمل ويحفظ البيانات
- ❌ نماذج عملية التعرف لم يتم إنشاؤها بعد

---

## 📚 المراجع المهمة
- `التدخلات اليومية المتقدم.md` - المواصفات الكاملة
- `جداول التدخلات المتقدم.md` - تفاصيل الجداول
- `ذاكرة التدخلات و الجداول.md` - التاريخ والتطوير السابق
- `Materiel_inv.md` - نظام الوسائل والتزامن

## 🎯 خطة العمل للوكيل الجديد

### الخطوة 1: الفهم والتحليل
1. قراءة الملفين المذكورين أعلاه بعناية
2. فحص الصفحتين الموجودتين لفهم الوضع الحالي
3. تحديد الفجوات والمتطلبات المفقودة

### الخطوة 2: التطوير المرحلي
1. **البدء بنماذج عملية التعرف** (الأولوية القصوى)
2. ربط النماذج بقاعدة البيانات
3. إنشاء APIs المطلوبة
4. اختبار التكامل بين الصفحتين

### الخطوة 3: التحقق والاختبار
1. التأكد من حفظ البيانات بشكل صحيح
2. التحقق من عرض البيانات في الجداول
3. اختبار سير العمل الكامل

### 🚨 تذكير مهم:
**لا تبدأ أي عمل قبل قراءة وفهم الملفين المذكورين أعلاه بالكامل!**

**آخر تحديث:** 1 أغسطس 2025
